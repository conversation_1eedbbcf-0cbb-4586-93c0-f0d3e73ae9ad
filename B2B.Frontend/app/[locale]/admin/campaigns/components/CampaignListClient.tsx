'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useCampaigns } from '@/hooks/api/useCampaigns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Pause,
  Edit,
  Trash2,
  Bar<PERSON>hart3,
  Eye
} from 'lucide-react';
import { CampaignListDto, CampaignStatus, CampaignType } from '@/types/campaign';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import Link from 'next/link';

export default function CampaignListClient() {
  const t = useTranslations('campaign');
  const commonT = useTranslations('common');
  const { campaigns, isLoading, activate, deactivate, delete: deleteCampaign } = useCampaigns();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([]);

  // Filter campaigns
  const filteredCampaigns = campaigns.filter((campaign) => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      campaign.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && campaign.status === CampaignStatus.Active) ||
      (statusFilter === 'inactive' && campaign.status === CampaignStatus.Inactive) ||
      (statusFilter === 'expired' && campaign.status === CampaignStatus.Expired) ||
      (statusFilter === 'draft' && campaign.status === CampaignStatus.Draft);

    const matchesType = typeFilter === 'all' || campaign.campaignType.toString() === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(filteredCampaigns.map(c => c.id));
    } else {
      setSelectedCampaigns([]);
    }
  };

  const handleSelectCampaign = (campaignId: string, checked: boolean) => {
    if (checked) {
      setSelectedCampaigns(prev => [...prev, campaignId]);
    } else {
      setSelectedCampaigns(prev => prev.filter(id => id !== campaignId));
    }
  };

  const getStatusBadge = (status: CampaignStatus) => {
    const variants = {
      [CampaignStatus.Active]: 'default',
      [CampaignStatus.Inactive]: 'secondary',
      [CampaignStatus.Draft]: 'outline',
      [CampaignStatus.Expired]: 'destructive',
      [CampaignStatus.LimitReached]: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status]}>
        {t(`statuses.${CampaignStatus[status]}`)}
      </Badge>
    );
  };

  const getCampaignTypeBadge = (type: CampaignType) => {
    return (
      <Badge variant="outline">
        {t(`campaignTypes.${CampaignType[type]}`)}
      </Badge>
    );
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {commonT('filters.title', { default: 'Filters' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('filters.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={t('fields.status', { default: 'Status' })} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('filters.all')}</SelectItem>
                <SelectItem value="active">{t('filters.active')}</SelectItem>
                <SelectItem value="inactive">{t('filters.inactive')}</SelectItem>
                <SelectItem value="expired">{t('filters.expired')}</SelectItem>
                <SelectItem value="draft">{t('filters.draft')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={t('filters.campaignType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('filters.all')}</SelectItem>
                <SelectItem value="1">{t('campaignTypes.CartDiscount')}</SelectItem>
                <SelectItem value="2">{t('campaignTypes.ProductDiscount')}</SelectItem>
                <SelectItem value="3">{t('campaignTypes.ShippingDiscount')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedCampaigns.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">
                {selectedCampaigns.length} {t('selected', { default: 'selected' })}
              </span>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  {t('actions.bulkActivate')}
                </Button>
                <Button variant="outline" size="sm">
                  {t('actions.bulkDeactivate')}
                </Button>
                <Button variant="destructive" size="sm">
                  {t('actions.bulkDelete')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Campaigns Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedCampaigns.length === filteredCampaigns.length && filteredCampaigns.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>{t('fields.name')}</TableHead>
                <TableHead>{t('fields.campaignType')}</TableHead>
                <TableHead>{t('fields.status')}</TableHead>
                <TableHead>{t('fields.startDate')}</TableHead>
                <TableHead>{t('fields.endDate')}</TableHead>
                <TableHead>{t('fields.usageCount')}</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCampaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedCampaigns.includes(campaign.id)}
                      onCheckedChange={(checked) => handleSelectCampaign(campaign.id, checked as boolean)}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      {campaign.description && (
                        <div className="text-sm text-muted-foreground line-clamp-1">
                          {campaign.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getCampaignTypeBadge(campaign.campaignType)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(campaign.status)}
                  </TableCell>
                  <TableCell>
                    {format(new Date(campaign.startDate), 'dd.MM.yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>
                    {format(new Date(campaign.endDate), 'dd.MM.yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>
                    <div className="text-center">
                      {campaign.usageCount}
                      {campaign.usageLimit && (
                        <span className="text-muted-foreground">/{campaign.usageLimit}</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/campaigns/${campaign.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            {commonT('actions.view')}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/campaigns/edit/${campaign.id}`}>
                            <Edit className="h-4 w-4 mr-2" />
                            {commonT('actions.edit')}
                          </Link>
                        </DropdownMenuItem>
                        {campaign.status === CampaignStatus.Active ? (
                          <DropdownMenuItem onClick={() => deactivate(campaign.id)}>
                            <Pause className="h-4 w-4 mr-2" />
                            {t('actions.deactivate')}
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => activate(campaign.id)}>
                            <Play className="h-4 w-4 mr-2" />
                            {t('actions.activate')}
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/reports/campaigns?campaignId=${campaign.id}`}>
                            <BarChart3 className="h-4 w-4 mr-2" />
                            {t('analytics.title')}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => deleteCampaign(campaign.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {commonT('actions.delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredCampaigns.length === 0 && (
            <div className="text-center py-12">
              <div className="text-muted-foreground">{t('messages.noData')}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

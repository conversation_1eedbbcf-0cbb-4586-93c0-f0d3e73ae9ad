"use client";

import { useState, useEffect } from 'react';
import { formatTLPrice } from '@/utils/currency';
import { useCustomerPoints } from '@/hooks/useCustomerPoints';

export default function PointUsage({ 
  orderSummary, 
  onPointsChange, 
  maxUsablePercentage = 50 
}) {
  const { pointBalance, loading, error } = useCustomerPoints();
  const [pointsToUse, setPointsToUse] = useState(0);
  const [isEnabled, setIsEnabled] = useState(false);

  // Maksimum kullanılabilir puan miktarını hesapla
  const maxUsableAmount = Math.floor(orderSummary.totalAmount * (maxUsablePercentage / 100));
  const maxUsablePoints = Math.min(pointBalance, maxUsableAmount);

  // Puan kullanımı değiştiğinde parent component'e bildir
  useEffect(() => {
    if (onPointsChange) {
      onPointsChange(isEnabled ? pointsToUse : 0);
    }
  }, [pointsToUse, isEnabled, onPointsChange]);

  // Puan kullanımını aç/kapat
  const handleTogglePoints = (enabled) => {
    setIsEnabled(enabled);
    if (!enabled) {
      setPointsToUse(0);
    }
  };

  // Puan miktarı değişikliği
  const handlePointsChange = (value) => {
    const numValue = parseInt(value) || 0;
    const clampedValue = Math.min(Math.max(0, numValue), maxUsablePoints);
    setPointsToUse(clampedValue);
  };

  // Maksimum puan kullan
  const useMaxPoints = () => {
    setPointsToUse(maxUsablePoints);
  };

  if (loading) {
    return (
      <div className="card mb-4">
        <div className="card-body">
          <h5 className="card-title">Puan Kullanımı</h5>
          <div className="text-center">
            <div className="spinner-border spinner-border-sm" role="status">
              <span className="visually-hidden">Yükleniyor...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || pointBalance <= 0) {
    return null; // Hata varsa veya puan yoksa component'i gösterme
  }

  return (
    <div className="card mb-4">
      <div className="card-body">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h5 className="card-title mb-0">Puan Kullanımı</h5>
          <div className="form-check form-switch">
            <input
              className="form-check-input"
              type="checkbox"
              id="enablePoints"
              checked={isEnabled}
              onChange={(e) => handleTogglePoints(e.target.checked)}
            />
            <label className="form-check-label" htmlFor="enablePoints">
              Puan Kullan
            </label>
          </div>
        </div>

        <div className="mb-3">
          <small className="text-muted">
            Mevcut Puan Bakiyeniz: <strong>{pointBalance.toLocaleString()} puan</strong>
          </small>
        </div>

        {isEnabled && (
          <div className="point-usage-controls">
            <div className="mb-3">
              <label htmlFor="pointsInput" className="form-label">
                Kullanılacak Puan Miktarı
              </label>
              <div className="input-group">
                <input
                  type="number"
                  className="form-control"
                  id="pointsInput"
                  value={pointsToUse}
                  onChange={(e) => handlePointsChange(e.target.value)}
                  min="0"
                  max={maxUsablePoints}
                  placeholder="0"
                />
                <span className="input-group-text">puan</span>
                <button
                  type="button"
                  className="btn btn-outline-primary"
                  onClick={useMaxPoints}
                  disabled={maxUsablePoints <= 0}
                >
                  Maksimum
                </button>
              </div>
              <div className="form-text">
                Maksimum {maxUsablePoints.toLocaleString()} puan kullanabilirsiniz 
                (sepet tutarının %{maxUsablePercentage}'si)
              </div>
            </div>

            {pointsToUse > 0 && (
              <div className="point-discount-info">
                <div className="d-flex justify-content-between">
                  <span>Puan İndirimi:</span>
                  <span className="text-success">
                    -{formatTLPrice(pointsToUse)} (1 puan = 1 TL)
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        {!isEnabled && pointBalance > 0 && (
          <div className="text-muted">
            <small>
              Puan kullanarak indirim yapabilirsiniz. 1 puan = 1 TL değerindedir.
            </small>
          </div>
        )}
      </div>
    </div>
  );
}

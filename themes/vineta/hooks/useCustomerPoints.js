"use client";

import { useState, useEffect } from 'react';
import { useCustomerAuth } from './useCustomerAuth';

export const useCustomerPoints = () => {
  const { session, isAuthenticated } = useCustomerAuth();
  const [pointBalance, setPointBalance] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchPointBalance = async () => {
    if (!isAuthenticated || !session?.customer?.id) {
      setPointBalance(0);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/customer/points/balance`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('<PERSON>uan bakiyesi alınamadı');
      }

      const data = await response.json();
      setPointBalance(data.balance || 0);
    } catch (err) {
      setError(err.message);
      setPointBalance(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPointBalance();
  }, [isAuthenticated, session?.customer?.id]);

  return {
    pointBalance,
    loading,
    error,
    refetch: fetchPointBalance
  };
};

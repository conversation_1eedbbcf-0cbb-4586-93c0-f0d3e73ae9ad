using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

/// <summary>
/// Kupon tanımları - hem genel hem de müşteri özel kuponlar
/// </summary>
[Table("Coupons")]
public class Coupon : BaseEntity
{
    /// <summary>
    /// Kupon kodu (benzersiz)
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string CouponCode { get; set; } = null!;

    /// <summary>
    /// Kupon adı/açıklaması
    /// </summary>
    [MaxLength(200)]
    public string? Name { get; set; }

    /// <summary>
    /// Kupon açıklaması
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// İndirim türü (Flat veya Percentage)
    /// </summary>
    public DiscountType DiscountType { get; set; }

    /// <summary>
    /// İndirim miktarı
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Son kullanma tarihi
    /// </summary>
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Kupon türü (Genel veya Müşteri Özel)
    /// </summary>
    public CouponType CouponType { get; set; }

    /// <summary>
    /// Eğer müşteri özel kupon ise, hangi müşteriye ait olduğu (null ise genel kupon)
    /// </summary>
    public Guid? CustomerId { get; set; }

    /// <summary>
    /// Toplam kullanım limiti (tüm müşteriler için)
    /// </summary>
    public int? TotalUsageLimit { get; set; }

    /// <summary>
    /// Müşteri başına kullanım limiti
    /// </summary>
    public int UsageLimitPerCustomer { get; set; } = 1;

    /// <summary>
    /// Toplam kullanım sayısı
    /// </summary>
    public int TotalUsageCount { get; set; } = 0;

    /// <summary>
    /// Minimum sepet tutarı
    /// </summary>
    public decimal? MinimumCartAmount { get; set; }

    /// <summary>
    /// Kupon aktif mi?
    /// </summary>
    public new bool IsActive { get; set; } = true;

    public Customer? Customer { get; set; }
    public ICollection<CouponUsage> CouponUsages { get; set; } = [];

    public static void Configure(EntityTypeBuilder<Coupon> builder)
    {
        // Indexes
        builder.HasIndex(c => c.CouponCode).IsUnique();
        builder.HasIndex(c => c.CouponType);
        builder.HasIndex(c => c.CustomerId);
        builder.HasIndex(c => c.ExpirationDate);
        builder.HasIndex(c => c.IsActive);

        // Decimal precision
        builder.Property(c => c.DiscountAmount).HasPrecision(18, 2);
        builder.Property(c => c.MinimumCartAmount).HasPrecision(18, 2);

        // Property configurations
        builder.Property(c => c.CustomerId)
            .IsRequired(false); // Nullable olduğunu açıkça belirt

        // Relationships
        builder.HasOne(c => c.Customer)
            .WithMany(c => c.Coupons)
            .HasForeignKey(c => c.CustomerId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(c => c.CouponUsages)
            .WithOne(cu => cu.Coupon)
            .HasForeignKey(cu => cu.CouponId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

/// <summary>
/// Kupon kullanım geçmişi
/// </summary>
[Table("CouponUsages")]
public class CouponUsage : BaseEntity
{
    /// <summary>
    /// Hangi kupon kullanıldı
    /// </summary>
    public Guid CouponId { get; set; }

    /// <summary>
    /// Hangi müşteri kullandı
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Hangi siparişte kullanıldı (opsiyonel)
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// Kullanım tarihi
    /// </summary>
    public DateTime UsageDate { get; set; }

    /// <summary>
    /// Uygulanan indirim miktarı
    /// </summary>
    public decimal DiscountApplied { get; set; }

    /// <summary>
    /// Sipariş tutarı (kupon uygulanmadan önce)
    /// </summary>
    public decimal OrderAmount { get; set; }

    // Navigation properties
    public Coupon Coupon { get; set; } = null!;
    public Customer Customer { get; set; } = null!;
    public Order? Order { get; set; }

    public static void Configure(EntityTypeBuilder<CouponUsage> builder)
    {
        // Indexes
        builder.HasIndex(cu => cu.CouponId);
        builder.HasIndex(cu => cu.CustomerId);
        builder.HasIndex(cu => cu.OrderId);
        builder.HasIndex(cu => cu.UsageDate);
        builder.HasIndex(cu => new { cu.CouponId, cu.CustomerId });

        // Decimal precision
        builder.Property(cu => cu.DiscountApplied).HasPrecision(18, 2);
        builder.Property(cu => cu.OrderAmount).HasPrecision(18, 2);

        // Relationships
        builder.HasOne(cu => cu.Coupon)
            .WithMany(c => c.CouponUsages)
            .HasForeignKey(cu => cu.CouponId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(cu => cu.Customer)
            .WithMany()
            .HasForeignKey(cu => cu.CustomerId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(cu => cu.Order)
            .WithMany()
            .HasForeignKey(cu => cu.OrderId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}

[Table("CouponsHistory")]
public class CouponHistory : HistoryBaseEntity
{
    public string CouponCode { get; set; } = null!;
    public string? Name { get; set; }
    public string? Description { get; set; }
    public DiscountType DiscountType { get; set; }
    public decimal DiscountAmount { get; set; }
    public DateTime ExpirationDate { get; set; }
    public CouponType CouponType { get; set; }
    public Guid? CustomerId { get; set; }
    public int? TotalUsageLimit { get; set; }
    public int UsageLimitPerCustomer { get; set; }
    public int TotalUsageCount { get; set; }
    public decimal? MinimumCartAmount { get; set; }
    public new bool IsActive { get; set; }
}

[Table("CouponUsagesHistory")]
public class CouponUsageHistory : HistoryBaseEntity
{
    public Guid CouponId { get; set; }
    public Guid CustomerId { get; set; }
    public Guid? OrderId { get; set; }
    public DateTime UsageDate { get; set; }
    public decimal DiscountApplied { get; set; }
    public decimal OrderAmount { get; set; }
}
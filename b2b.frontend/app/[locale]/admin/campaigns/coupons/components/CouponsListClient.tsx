'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Users,
  Percent,
  DollarSign,
  Gift
} from 'lucide-react';
import { toast } from 'sonner';
import { api } from '@/lib/api/client';
import AddCouponModal from './AddCouponModal';

interface CouponsListClientProps {
  type: 'all' | 'general' | 'customer-specific';
}

interface CouponListDto {
  id: string;
  couponCode: string;
  couponType: 'CustomerSpecific' | 'General';
  customerId?: string;
  customerName?: string;
  discountType: 'Percentage' | 'FixedAmount';
  discountValue: number;
  totalUsageLimit?: number;
  usageLimitPerCustomer: number;
  totalUsageCount: number;
  isActive: boolean;
  expirationDate?: string;
  createdAt: string;
}

export default function CouponsListClient({ type }: CouponsListClientProps) {
  const t = useTranslations('admin.coupons');
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // API endpoint'ini type'a göre belirle
  const getApiEndpoint = () => {
    switch (type) {
      case 'general':
        return '/api/coupon/general';
      case 'customer-specific':
        return '/api/coupon/customer-specific';
      default:
        return '/api/coupon';
    }
  };

  const { data: coupons, isLoading, refetch } = useQuery({
    queryKey: ['coupons', type],
    queryFn: async () => {
      const response = await api.get(getApiEndpoint());
      return response.data as CouponListDto[];
    },
  });

  // Arama filtresi
  const filteredCoupons = coupons?.filter(coupon =>
    coupon.couponCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    coupon.customerName?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleDelete = async (couponId: string) => {
    try {
      await api.delete(`/api/coupon/${couponId}`);
      toast.success(t('messages.deleteSuccess'));
      refetch();
    } catch (error) {
      toast.error(t('messages.deleteError'));
    }
  };

  const getStatusBadge = (coupon: CouponListDto) => {
    if (!coupon.isActive) {
      return <Badge variant="secondary">{t('status.inactive')}</Badge>;
    }
    
    if (coupon.expirationDate && new Date(coupon.expirationDate) < new Date()) {
      return <Badge variant="destructive">{t('status.expired')}</Badge>;
    }

    if (coupon.totalUsageLimit && coupon.totalUsageCount >= coupon.totalUsageLimit) {
      return <Badge variant="destructive">{t('status.usedUp')}</Badge>;
    }

    return <Badge variant="default">{t('status.active')}</Badge>;
  };

  const getTypeBadge = (couponType: string) => {
    return couponType === 'General' 
      ? <Badge variant="outline">{t('type.general')}</Badge>
      : <Badge variant="outline">{t('type.customerSpecific')}</Badge>;
  };

  const getDiscountDisplay = (coupon: CouponListDto) => {
    return coupon.discountType === 'Percentage' 
      ? `%${coupon.discountValue}`
      : `₺${coupon.discountValue}`;
  };

  const getUsageDisplay = (coupon: CouponListDto) => {
    if (coupon.totalUsageLimit) {
      return `${coupon.totalUsageCount}/${coupon.totalUsageLimit}`;
    }
    return coupon.totalUsageCount.toString();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('search.placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-[300px]"
            />
          </div>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t('actions.addCoupon')}
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">{t('loading')}</div>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('table.couponCode')}</TableHead>
                <TableHead>{t('table.type')}</TableHead>
                <TableHead>{t('table.customer')}</TableHead>
                <TableHead>{t('table.discount')}</TableHead>
                <TableHead>{t('table.usage')}</TableHead>
                <TableHead>{t('table.expiration')}</TableHead>
                <TableHead>{t('table.status')}</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCoupons.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <Gift className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">{t('empty.message')}</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredCoupons.map((coupon) => (
                  <TableRow key={coupon.id}>
                    <TableCell className="font-medium">{coupon.couponCode}</TableCell>
                    <TableCell>{getTypeBadge(coupon.couponType)}</TableCell>
                    <TableCell>
                      {coupon.customerName || '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        {coupon.discountType === 'Percentage' ? (
                          <Percent className="h-3 w-3" />
                        ) : (
                          <DollarSign className="h-3 w-3" />
                        )}
                        <span>{getDiscountDisplay(coupon)}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getUsageDisplay(coupon)}</TableCell>
                    <TableCell>
                      {coupon.expirationDate ? (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(coupon.expirationDate).toLocaleDateString()}</span>
                        </div>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(coupon)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            {t('actions.view')}
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            {t('actions.edit')}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(coupon.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t('actions.delete')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}

      <AddCouponModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          refetch();
          setIsAddModalOpen(false);
        }}
      />
    </div>
  );
}
